package com.apm.applicationperformancemonitoring.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "tbl_trace")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Trace {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY) // or other strategy
    @Column(name = "id")
    private long id;

    @Column(name = "trace_id")
    private String traceID;

    @Column(name = "service_name")
    private String serviceName;

    @Column(name = "root_span_name")
    private String rootSpanName;

    @Column(name = "duration")
    private int duration;

    @Column(name = "start_time")
    private String startTime;

    @Column(name = "spans")
    private int spans;

    @Column(name = "end_time")
    private String endTime;

    @Column(name = "root_span_id")
    private String rootSpanId;
}
