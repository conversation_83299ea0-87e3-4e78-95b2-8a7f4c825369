package com.apm.applicationperformancemonitoring.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "tbl_span")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Span {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY) // or other strategy
    @Column(name = "id")
    private long id;

    @Column(name = "trace_id")
    private String traceId;

    @Column(name = "span_id")
    private String spanId;

    @Column(name = "service_name")
    private String serviceName;

    @Column(name = "duration")
    private long duration;

    @Column(name = "parent_span_id")
    private String parentSpanId;

    @Column(name = "span_name")
    private String spanName;

}
