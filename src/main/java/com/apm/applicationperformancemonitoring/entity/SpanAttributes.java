package com.apm.applicationperformancemonitoring.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "tbl_span_attributes")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SpanAttributes {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private long id;

    @Column(name = "trace_id")
    private String traceId;

    @Column(name = "span_id")
    private String spanId;

    @Column(columnDefinition = "jsonb")
    private String attributes;

}
