package com.apm.applicationperformancemonitoring.services;

import com.apm.applicationperformancemonitoring.entity.Trace;
import com.apm.applicationperformancemonitoring.repository.SpanRepository;
import com.apm.applicationperformancemonitoring.repository.TraceRepository;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.protobuf.ByteString;
import io.opentelemetry.proto.collector.trace.v1.ExportTraceServiceRequest;
import io.opentelemetry.proto.common.v1.AnyValue;
import io.opentelemetry.proto.common.v1.ArrayValue;
import io.opentelemetry.proto.common.v1.KeyValue;
import io.opentelemetry.proto.common.v1.KeyValueList;
import io.opentelemetry.proto.resource.v1.Resource;
import io.opentelemetry.proto.trace.v1.ResourceSpans;
import io.opentelemetry.proto.trace.v1.ScopeSpans;
import io.opentelemetry.proto.trace.v1.Span;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HexFormat;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class TraceService {

    private static final Logger logger = LoggerFactory.getLogger(TraceService.class);

    @Autowired
    private SpanRepository spanRepository;

    @Autowired
    private TraceRepository traceRepository;

    public void processTraceData(ExportTraceServiceRequest request) {

        try {
            // process trace....
            Map<String, Trace> traceEntityByTraceId = new HashMap<>();

            for (ResourceSpans resourceSpans : request.getResourceSpansList()) {
                Resource resource = resourceSpans.getResource();
                Map<String, Object> resourceAttributes = parseAttribute(resource.getAttributesList());
                String serviceName = resourceAttributes.getOrDefault("service.name", "Unknown_service").toString();

                for (ScopeSpans scopeSpans : resourceSpans.getScopeSpansList()) {
                    for (Span span : scopeSpans.getSpansList()) {
                        processSpan(span, serviceName, traceEntityByTraceId);
                    }
                }

                traceRepository.saveAll(traceEntityByTraceId.values());
            }

        } catch (Exception exception) {
            logger.error(exception.getMessage());
        }
    }

    private void processSpan(Span span, String serviceName, Map<String, Trace> traceEntityByTraceId) {

        Map<String, Object> attributes = parseAttribute(span.getAttributesList());
        Trace traceEntity = traceEntityByTraceId.computeIfAbsent(convertHex(span.getTraceId()), value -> new Trace());

        populateTrace(traceEntity, span, serviceName);

        com.apm.applicationperformancemonitoring.entity.Span spanEntity = new com.apm.applicationperformancemonitoring.entity.Span();
        spanEntity.setTraceId(convertHex(span.getTraceId()));
        spanEntity.setSpanId(convertHex(span.getSpanId()));
        spanEntity.setServiceName(serviceName);
        spanEntity.setDuration(convertTime(span.getEndTimeUnixNano() - span.getStartTimeUnixNano(), TimeUnit.NANOSECONDS, TimeUnit.MILLISECONDS));
        spanEntity.setParentSpanId(convertHex(span.getParentSpanId()));
        spanEntity.setSpanName(span.getName());

        spanRepository.save(spanEntity);

    }

    private void populateTrace(Trace trace, Span span, String serviceName) {
        trace.setSpans(trace.getSpans() + 1);

        if (trace.getTraceID() == null) {
            trace.setTraceID(convertHex(span.getTraceId()));
        }

        if (trace.getServiceName() == null || trace.getServiceName().isEmpty()) {
            trace.setServiceName(serviceName);
        }

        if (trace.getStartTime() == null || trace.getStartTime().isEmpty() || span.getStartTimeUnixNano() < Long.parseLong(trace.getStartTime())) {
            // this is the smaller start time
            trace.setStartTime(Long.toString(span.getStartTimeUnixNano()));
        }

        if (trace.getEndTime() == null || trace.getEndTime().isEmpty() || span.getEndTimeUnixNano() > Long.parseLong(trace.getEndTime())) {
            // this is the larger end time
            trace.setEndTime(Long.toString(span.getEndTimeUnixNano()));
        }

        if (span.getParentSpanId().isEmpty()) {
            trace.setRootSpanId(convertHex(span.getSpanId()));
            trace.setRootSpanName(span.getName());
        }
    }

    public static String convertHex(ByteString id) {
        return HexFormat.of().formatHex(id.toByteArray());
    }

    private Map<String, Object> parseAttribute(List<KeyValue> attributes) {

        Map<String, Object> attribute = new HashMap<>();

        try {
            if (attributes != null && !attributes.isEmpty()) {
                for (var keyValue : attributes) {
                    attribute.put(keyValue.getKey(), parseAttributeValue(keyValue.getValue(), logger));
                }
            }
        } catch (Exception exception) {
            logger.error(exception.getMessage());
        }

        return attribute;
    }

    public static Object parseAttributeValue(AnyValue value, Logger logger) {
        return switch (value.getValueCase()) {
            case INT_VALUE -> value.getIntValue();
            case STRING_VALUE -> value.getStringValue();
            case BOOL_VALUE -> value.getBoolValue();
            case BYTES_VALUE -> value.getBytesValue();
            case DOUBLE_VALUE -> value.getDoubleValue();
            case ARRAY_VALUE -> parseArrayValue(value.getArrayValue(), logger);
            case KVLIST_VALUE -> parseKeyValue(value.getKvlistValue(), logger);
            case VALUE_NOT_SET -> "";
        };
    }

    private static ArrayNode parseArrayValue(ArrayValue value, Logger logger) {
        ObjectMapper mapper = new ObjectMapper();
        ArrayNode lists = mapper.createArrayNode();

        try {
            if (value != null) {
                for (var attribute : value.getValuesList()) {
                    lists.add((JsonNode) parseAttributeValue(attribute, logger));
                }
            }
        }
        catch (Exception exception) {
            logger.error(exception.getMessage());
        }

        return lists;
    }

    private static ObjectNode parseKeyValue(KeyValueList value, Logger logger) {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode values = mapper.createObjectNode();

        try {
            if (value != null) {
                for (var keyValue : value.getValuesList()) {
                    values.put(keyValue.getKey(), (JsonNode) parseAttributeValue(keyValue.getValue(), logger));
                }
            }
        } catch (Exception exception) {
            logger.error(exception.getMessage());
        }

        return values;
    }

    public long convertTime(long duration, TimeUnit fromUnit, TimeUnit toUnit) {
        return toUnit.convert(duration, fromUnit);
    }

}
